-- RLS Policies for tenant isolation

-- Tenants policies
CREATE POLICY "Users can view their own tenant" ON tenants FOR SELECT USING (
    id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    )
);

-- Users policies
CREATE POLICY "Users can view users in their tenant" ON users FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    )
);

CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (
    id = auth.uid()
);

-- Wallets policies
CREATE POLICY "Users can view wallets in their tenant" ON wallets FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    )
);

-- Wallet transactions policies
CREATE POLICY "Users can view transactions in their tenant" ON wallet_transactions FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    )
);

-- Categories policies
CREATE POLICY "Users can view categories in their tenant" ON categories FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Products policies
CREATE POLICY "Users can view products in their tenant" ON products FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Product pricing policies
CREATE POLICY "Users can view pricing in their tenant" ON product_pricing FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Product packages policies
CREATE POLICY "Users can view packages in their tenant" ON product_packages FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Custom fields policies
CREATE POLICY "Users can view custom fields in their tenant" ON product_custom_fields FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Custom field options policies
CREATE POLICY "Users can view field options in their tenant" ON custom_field_options FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Digital codes policies (restricted access)
CREATE POLICY "Only admins can view digital codes" ON digital_codes FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid() AND role = 'admin'
    ) AND deleted_at IS NULL
);

-- Banners policies
CREATE POLICY "Users can view banners in their tenant" ON banners FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Homepage layout policies
CREATE POLICY "Users can view layout in their tenant" ON homepage_layout FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Orders policies
CREATE POLICY "Users can view orders in their tenant" ON orders FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);

-- Order items policies
CREATE POLICY "Users can view order items in their tenant" ON order_items FOR SELECT USING (
    tenant_id IN (
        SELECT tenant_id FROM users WHERE id = auth.uid()
    ) AND deleted_at IS NULL
);
