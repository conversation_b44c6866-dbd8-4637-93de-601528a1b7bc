<template>
  <div class="fixed inset-y-0 left-0 w-64 bg-gray-900 text-white">
    <div class="flex items-center justify-center h-16 bg-gray-800">
      <h1 class="text-xl font-bold">Admin Panel</h1>
    </div>
    
    <nav class="mt-8">
      <div class="px-4 space-y-2">
        <NuxtLink 
          to="/admin" 
          class="flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700"
          :class="{ 'bg-gray-700': $route.path === '/admin' }"
        >
          <BarChart3 class="w-5 h-5 mr-3" />
          Overview
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/users" 
          class="flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700"
          :class="{ 'bg-gray-700': $route.path.startsWith('/admin/users') }"
        >
          <Users class="w-5 h-5 mr-3" />
          Users
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/products" 
          class="flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700"
          :class="{ 'bg-gray-700': $route.path.startsWith('/admin/products') }"
        >
          <Package class="w-5 h-5 mr-3" />
          Products
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/categories" 
          class="flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700"
          :class="{ 'bg-gray-700': $route.path.startsWith('/admin/categories') }"
        >
          <Folder class="w-5 h-5 mr-3" />
          Categories
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/orders" 
          class="flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700"
          :class="{ 'bg-gray-700': $route.path.startsWith('/admin/orders') }"
        >
          <ShoppingCart class="w-5 h-5 mr-3" />
          Orders
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/homepage" 
          class="flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700"
          :class="{ 'bg-gray-700': $route.path.startsWith('/admin/homepage') }"
        >
          <Layout class="w-5 h-5 mr-3" />
          Homepage
        </NuxtLink>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { BarChart3, Users, Package, Folder, ShoppingCart, Layout } from 'lucide-vue-next'
</script>
