<template>
  <header class="bg-white shadow-sm border-b h-16 flex items-center justify-between px-6">
    <div>
      <h2 class="text-lg font-semibold text-gray-900">{{ pageTitle }}</h2>
    </div>
    
    <div class="flex items-center space-x-4">
      <span class="text-sm text-gray-600">{{ user?.email }}</span>
      <button @click="signOut" class="btn-outline">Sign Out</button>
    </div>
  </header>
</template>

<script setup>
import { useSupabaseClient, useSupabaseUser, useRoute, computed, navigateTo } from '#imports'

const supabase = useSupabaseClient()
const user = useSupabaseUser()
const route = useRoute()

const pageTitle = computed(() => {
  const path = route.path
  if (path === '/admin') return 'Overview'
  if (path.startsWith('/admin/users')) return 'Users Management'
  if (path.startsWith('/admin/products')) return 'Products Management'
  if (path.startsWith('/admin/categories')) return 'Categories Management'
  if (path.startsWith('/admin/orders')) return 'Orders Management'
  if (path.startsWith('/admin/homepage')) return 'Homepage Builder'
  return 'Admin Panel'
})

const signOut = async () => {
  await supabase.auth.signOut()
  await navigateTo('/auth/login')
}
</script>
