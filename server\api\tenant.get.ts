import { defineEvent<PERSON><PERSON><PERSON> } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"
import { createError } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  const { data: userData } = await supabase.from("users").select("tenant_id").eq("id", user.id).single()

  if (!userData) {
    throw createError({
      statusCode: 404,
      statusMessage: "User not found",
    })
  }

  const { data: tenant } = await supabase.from("tenants").select("*").eq("id", userData.tenant_id).single()

  return { data: tenant }
})
