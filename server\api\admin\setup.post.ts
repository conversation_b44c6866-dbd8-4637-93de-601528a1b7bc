import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from "h3"
import { serverSupabaseClient } from "#supabase/server"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)

  // Get or create the first tenant
  let { data: tenant } = await supabase.from("tenants").select("id").limit(1).single()

  if (!tenant) {
    const { data: newTenant } = await supabase
      .from("tenants")
      .insert({
        name: "Demo Store",
        domain: "demo.localhost",
        settings: { theme: "default", currency: "USD" },
      })
      .select()
      .single()
    tenant = newTenant
  }

  // Create sample categories
  const categories = [
    { name: "Electronics", image_url: "/placeholder.svg?height=200&width=200&text=Electronics" },
    { name: "Clothing", image_url: "/placeholder.svg?height=200&width=200&text=Clothing" },
    { name: "Books", image_url: "/placeholder.svg?height=200&width=200&text=Books" },
    { name: "Home & Garden", image_url: "/placeholder.svg?height=200&width=200&text=Home" },
  ]

  const { data: createdCategories } = await supabase
    .from("categories")
    .upsert(
      categories.map((cat, index) => ({
        tenant_id: tenant.id,
        name: cat.name,
        image_url: cat.image_url,
        sort_order: index + 1,
      })),
    )
    .select()

  // Create sample products
  const products = [
    {
      name: "Smartphone Pro",
      description: "Latest smartphone with advanced features and great camera",
      image_url: "/placeholder.svg?height=300&width=300&text=Smartphone",
      category_id: createdCategories?.[0]?.id,
      pricing: { original_price: 800, user_price: 900, distributor_price: 850 },
    },
    {
      name: "Wireless Headphones",
      description: "Premium wireless headphones with noise cancellation",
      image_url: "/placeholder.svg?height=300&width=300&text=Headphones",
      category_id: createdCategories?.[0]?.id,
      pricing: { original_price: 150, user_price: 200, distributor_price: 175 },
    },
    {
      name: "Cotton T-Shirt",
      description: "Comfortable cotton t-shirt in various colors",
      image_url: "/placeholder.svg?height=300&width=300&text=T-Shirt",
      category_id: createdCategories?.[1]?.id,
      pricing: { original_price: 15, user_price: 25, distributor_price: 20 },
    },
    {
      name: "Programming Guide",
      description: "Complete guide to modern web development",
      image_url: "/placeholder.svg?height=300&width=300&text=Book",
      category_id: createdCategories?.[2]?.id,
      pricing: { original_price: 30, user_price: 45, distributor_price: 35 },
    },
  ]

  for (const product of products) {
    const { data: createdProduct } = await supabase
      .from("products")
      .insert({
        tenant_id: tenant.id,
        name: product.name,
        description: product.description,
        image_url: product.image_url,
        category_id: product.category_id,
        is_active: true,
      })
      .select()
      .single()

    // Add pricing
    await supabase.from("product_pricing").insert({
      tenant_id: tenant.id,
      product_id: createdProduct.id,
      original_price: product.pricing.original_price,
      user_price: product.pricing.user_price,
      distributor_price: product.pricing.distributor_price,
    })
  }

  return { success: true }
})
