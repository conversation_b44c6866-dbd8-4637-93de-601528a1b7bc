import { ref, computed } from "vue"

interface CartItem {
  id: string
  product_id: string
  package_id?: string
  name: string
  image_url?: string
  price: number
  quantity: number
  custom_fields?: Record<string, any>
}

const cartItems = ref<CartItem[]>([])

export const useCart = () => {
  const addToCart = (item: Omit<CartItem, "quantity">) => {
    const existingItem = cartItems.value.find(
      (cartItem) => cartItem.product_id === item.product_id && cartItem.package_id === item.package_id,
    )

    if (existingItem) {
      existingItem.quantity += 1
    } else {
      cartItems.value.push({ ...item, quantity: 1 })
    }
  }

  const removeFromCart = (productId: string, packageId?: string) => {
    const index = cartItems.value.findIndex((item) => item.product_id === productId && item.package_id === packageId)
    if (index > -1) {
      cartItems.value.splice(index, 1)
    }
  }

  const updateQuantity = (productId: string, quantity: number, packageId?: string) => {
    const item = cartItems.value.find(
      (cartItem) => cartItem.product_id === productId && cartItem.package_id === packageId,
    )
    if (item) {
      if (quantity <= 0) {
        removeFromCart(productId, packageId)
      } else {
        item.quantity = quantity
      }
    }
  }

  const clearCart = () => {
    cartItems.value = []
  }

  const totalItems = computed(() => cartItems.value.reduce((sum, item) => sum + item.quantity, 0))

  const totalPrice = computed(() => cartItems.value.reduce((sum, item) => sum + item.price * item.quantity, 0))

  return {
    items: cartItems,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
  }
}
