<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Packages</h3>
      <button @click="addPackage" class="btn-primary">
        <Plus class="w-4 h-4 mr-2" />
        Add Package
      </button>
    </div>
    
    <div v-if="localPackages.length === 0" class="text-center py-8 text-gray-500">
      No packages added yet. Click "Add Package" to get started.
    </div>
    
    <div v-else class="space-y-4">
      <div 
        v-for="(pkg, index) in localPackages" 
        :key="pkg.id || index"
        class="card p-4"
      >
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">Package {{ index + 1 }}</h4>
          <button @click="removePackage(index)" class="text-red-600 hover:text-red-800">
            <Trash2 class="w-4 h-4" />
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">Package Name *</label>
            <input 
              v-model="pkg.name"
              type="text" 
              class="input"
              placeholder="Enter package name"
              required
            />
          </div>
          
          <div class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">Image URL</label>
            <input 
              v-model="pkg.image_url"
              type="url" 
              class="input"
              placeholder="https://example.com/image.jpg"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Original Price *</label>
            <input 
              v-model.number="pkg.original_price"
              type="number" 
              step="0.01"
              class="input"
              placeholder="0.00"
              required
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">User Price *</label>
            <input 
              v-model.number="pkg.user_price"
              type="number" 
              step="0.01"
              class="input"
              placeholder="0.00"
              required
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Distributor Price</label>
            <input 
              v-model.number="pkg.distributor_price"
              type="number" 
              step="0.01"
              class="input"
              placeholder="0.00"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Discount Price</label>
            <input 
              v-model.number="pkg.discount_price"
              type="number" 
              step="0.01"
              class="input"
              placeholder="0.00"
            />
          </div>
        </div>
        
        <div v-if="getPackageProfit(pkg) !== null" class="mt-4 p-3 bg-green-50 rounded-lg">
          <p class="text-sm font-medium text-green-800">
            Estimated Profit: ${{ getPackageProfit(pkg).toFixed(2) }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Plus, Trash2 } from 'lucide-vue-next'
import { ref, watch } from 'vue'

const props = defineProps({
  packages: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:packages'])

const localPackages = ref([...props.packages])

const addPackage = () => {
  localPackages.value.push({
    name: '',
    image_url: '',
    original_price: 0,
    user_price: 0,
    distributor_price: null,
    discount_price: null,
    sort_order: localPackages.value.length
  })
}

const removePackage = (index) => {
  localPackages.value.splice(index, 1)
}

const getPackageProfit = (pkg) => {
  if (!pkg.user_price || !pkg.original_price) {
    return null
  }
  return pkg.user_price - pkg.original_price
}

watch(localPackages, (newValue) => {
  emit('update:packages', newValue)
}, { deep: true })

watch(() => props.packages, (newValue) => {
  localPackages.value = [...newValue]
}, { deep: true })
</script>
