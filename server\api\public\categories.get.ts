import { defineEventHand<PERSON> } from "h3"
import { serverSupabaseClient } from "#supabase/server"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)

  // For demo purposes, let's use the first tenant
  // In production, you'd determine tenant from domain/subdomain
  const { data: tenant } = await supabase.from("tenants").select("id").limit(1).single()

  if (!tenant) {
    return []
  }

  const { data: categories } = await supabase
    .from("categories")
    .select("*")
    .eq("tenant_id", tenant.id)
    .is("deleted_at", null)
    .order("sort_order")

  return categories || []
})
