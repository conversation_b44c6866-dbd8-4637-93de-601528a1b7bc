<template>
  <div class="space-y-4">
    <h3 class="text-lg font-semibold">Pricing</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium mb-2">Original Price *</label>
        <input 
          v-model.number="localPricing.original_price"
          type="number" 
          step="0.01"
          class="input"
          placeholder="0.00"
          required
        />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-2">User Price *</label>
        <input 
          v-model.number="localPricing.user_price"
          type="number" 
          step="0.01"
          class="input"
          placeholder="0.00"
          required
        />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-2">Distributor Price</label>
        <input 
          v-model.number="localPricing.distributor_price"
          type="number" 
          step="0.01"
          class="input"
          placeholder="0.00"
        />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-2">Discount Price</label>
        <input 
          v-model.number="localPricing.discount_price"
          type="number" 
          step="0.01"
          class="input"
          placeholder="0.00"
        />
      </div>
    </div>
    
    <div v-if="profit !== null" class="p-4 bg-green-50 rounded-lg">
      <p class="text-sm font-medium text-green-800">
        Estimated Profit: ${{ profit.toFixed(2) }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  pricing: {
    type: Object,
    default: () => ({
      original_price: 0,
      user_price: 0,
      distributor_price: null,
      discount_price: null
    })
  }
})

const emit = defineEmits(['update:pricing'])

const localPricing = ref({ ...props.pricing })

const profit = computed(() => {
  if (!localPricing.value.user_price || !localPricing.value.original_price) {
    return null
  }
  return localPricing.value.user_price - localPricing.value.original_price
})

watch(localPricing, (newValue) => {
  emit('update:pricing', newValue)
}, { deep: true })

watch(() => props.pricing, (newValue) => {
  localPricing.value = { ...newValue }
}, { deep: true })
</script>
