import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"
import { createError } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  // Verify admin role
  const { data: userData } = await supabase.from("users").select("tenant_id, role").eq("id", user.id).single()

  if (userData?.role !== "admin") {
    throw createError({
      statusCode: 403,
      statusMessage: "Access denied",
    })
  }

  // Get stats
  const [{ count: totalUsers }, { count: totalProducts }, { count: totalOrders }, { data: earnings }] =
    await Promise.all([
      supabase
        .from("users")
        .select("*", { count: "exact", head: true })
        .eq("tenant_id", userData.tenant_id)
        .is("deleted_at", null),

      supabase
        .from("products")
        .select("*", { count: "exact", head: true })
        .eq("tenant_id", userData.tenant_id)
        .is("deleted_at", null),

      supabase
        .from("orders")
        .select("*", { count: "exact", head: true })
        .eq("tenant_id", userData.tenant_id)
        .is("deleted_at", null),

      supabase
        .from("orders")
        .select("total_amount")
        .eq("tenant_id", userData.tenant_id)
        .eq("status", "completed")
        .is("deleted_at", null),
    ])

  const totalEarnings = earnings?.reduce((sum, order) => sum + Number.parseFloat(order.total_amount), 0) || 0

  return {
    totalUsers: totalUsers || 0,
    totalProducts: totalProducts || 0,
    totalOrders: totalOrders || 0,
    totalEarnings,
  }
})
