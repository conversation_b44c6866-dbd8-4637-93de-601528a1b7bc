<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
      <!-- Filters Sidebar -->
      <div class="lg:w-64 flex-shrink-0">
        <div class="card p-4">
          <h3 class="font-semibold mb-4">Categories</h3>
          <div class="space-y-2">
            <label class="flex items-center">
              <input 
                v-model="selectedCategory" 
                type="radio" 
                value="" 
                class="mr-2"
              />
              All Categories
            </label>
            <label 
              v-for="category in categories" 
              :key="category.id"
              class="flex items-center"
            >
              <input 
                v-model="selectedCategory" 
                type="radio" 
                :value="category.id" 
                class="mr-2"
              />
              {{ category.name }}
            </label>
          </div>
        </div>
      </div>
      
      <!-- Products Grid -->
      <div class="flex-1">
        <div class="flex items-center justify-between mb-6">
          <h1 class="text-2xl font-bold">
            {{ selectedCategoryName || 'All Products' }}
          </h1>
          <div class="text-sm text-gray-600">
            {{ filteredProducts.length }} products found
          </div>
        </div>
        
        <div v-if="pending" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="i in 6" :key="i" class="card p-4 animate-pulse">
            <div class="aspect-square bg-gray-200 rounded-lg mb-4"></div>
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-3 bg-gray-200 rounded mb-4"></div>
            <div class="h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
        
        <div v-else-if="filteredProducts.length === 0" class="text-center py-12">
          <div class="text-gray-500 mb-4">No products found</div>
          <NuxtLink to="/" class="btn-primary">Back to Home</NuxtLink>
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ProductCard 
            v-for="product in filteredProducts" 
            :key="product.id"
            :product="product"
            :pricing="product.pricing?.[0]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, navigateTo } from '#app'
import { useAsyncData } from '#app/composables/asyncData'
import { useSeoMeta } from '#app/composables/seoMeta'
import { $fetch } from '#app/utils/fetch'

const selectedCategory = ref(useRoute().query.category || '')

const { data: categories } = await useAsyncData('categories', () => 
  $fetch('/api/public/categories')
)

const { data: products, pending } = await useAsyncData('products', () => 
  $fetch('/api/public/products', {
    query: {
      category: selectedCategory.value || undefined
    }
  }), {
    watch: [selectedCategory]
  }
)

const filteredProducts = computed(() => products.value || [])

const selectedCategoryName = computed(() => {
  if (!selectedCategory.value) return null
  const category = categories.value?.find(c => c.id === selectedCategory.value)
  return category?.name
})

watch(selectedCategory, (newValue) => {
  navigateTo({
    path: '/shop',
    query: newValue ? { category: newValue } : {}
  })
})

useSeoMeta({
  title: 'Shop - Store',
  description: 'Browse our products'
})
</script>
