<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold mb-4">Store Setup</h1>
      <p class="text-gray-600">Let's get your store ready with some initial data</p>
    </div>

    <div class="space-y-8">
      <!-- Quick Setup Button -->
      <div class="card p-6 text-center">
        <h2 class="text-xl font-semibold mb-4">Quick Setup</h2>
        <p class="text-gray-600 mb-6">
          This will create sample categories and products to get you started quickly.
        </p>
        <button 
          @click="runQuickSetup" 
          :disabled="loading"
          class="btn-primary"
        >
          {{ loading ? 'Setting up...' : 'Run Quick Setup' }}
        </button>
      </div>

      <!-- Manual Setup Options -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="card p-6">
          <h3 class="text-lg font-semibold mb-4">Categories</h3>
          <p class="text-gray-600 mb-4">Create product categories to organize your store</p>
          <NuxtLink to="/admin/categories" class="btn-outline">
            Manage Categories
          </NuxtLink>
        </div>

        <div class="card p-6">
          <h3 class="text-lg font-semibold mb-4">Products</h3>
          <p class="text-gray-600 mb-4">Add products to your store</p>
          <NuxtLink to="/admin/products" class="btn-outline">
            Manage Products
          </NuxtLink>
        </div>
      </div>

      <!-- Current Status -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">Current Status</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div class="text-2xl font-bold text-blue-600">{{ stats.categories || 0 }}</div>
            <div class="text-sm text-gray-600">Categories</div>
          </div>
          <div>
            <div class="text-2xl font-bold text-green-600">{{ stats.products || 0 }}</div>
            <div class="text-sm text-gray-600">Products</div>
          </div>
          <div>
            <div class="text-2xl font-bold text-purple-600">{{ stats.users || 0 }}</div>
            <div class="text-sm text-gray-600">Users</div>
          </div>
          <div>
            <div class="text-2xl font-bold text-orange-600">{{ stats.orders || 0 }}</div>
            <div class="text-sm text-gray-600">Orders</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { $fetch, navigateTo, definePageMeta } from '#app'

const loading = ref(false)
const stats = ref({
  categories: 0,
  products: 0,
  users: 0,
  orders: 0
})

const runQuickSetup = async () => {
  loading.value = true
  
  try {
    await $fetch('/api/admin/setup', {
      method: 'POST'
    })
    
    // Refresh the page to show new data
    await navigateTo('/')
  } catch (error) {
    console.error('Setup failed:', error)
    alert('Setup failed. Please try again.')
  } finally {
    loading.value = false
  }
}

// Load current stats
const loadStats = async () => {
  try {
    const data = await $fetch('/api/admin/stats')
    stats.value = data
  } catch (error) {
    console.log('Could not load stats:', error)
  }
}

onMounted(() => {
  loadStats()
})

definePageMeta({
  layout: 'admin'
})
</script>
