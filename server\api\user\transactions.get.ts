import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from "h3"
import { serverSupabaseClient } from "#supabase/server"
import { serverSupabaseUser } from "#supabase/server"
import { createError } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  const { data: transactions } = await supabase
    .from("wallet_transactions")
    .select("*")
    .eq("user_id", user.id)
    .is("deleted_at", null)
    .order("created_at", { ascending: false })
    .limit(50)

  return transactions || []
})
