<template>
  <div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">Total Earnings</p>
            <p class="text-2xl font-bold text-green-600">${{ stats.totalEarnings.toFixed(2) }}</p>
          </div>
          <DollarSign class="w-8 h-8 text-green-600" />
        </div>
      </div>
      
      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">Total Users</p>
            <p class="text-2xl font-bold text-blue-600">{{ stats.totalUsers }}</p>
          </div>
          <Users class="w-8 h-8 text-blue-600" />
        </div>
      </div>
      
      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">Total Products</p>
            <p class="text-2xl font-bold text-purple-600">{{ stats.totalProducts }}</p>
          </div>
          <Package class="w-8 h-8 text-purple-600" />
        </div>
      </div>
      
      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">Total Orders</p>
            <p class="text-2xl font-bold text-orange-600">{{ stats.totalOrders }}</p>
          </div>
          <ShoppingCart class="w-8 h-8 text-orange-600" />
        </div>
      </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="card">
      <div class="p-4 border-b">
        <h2 class="text-lg font-semibold">Recent Orders</h2>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Order</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="order in recentOrders" :key="order.id">
              <td class="px-4 py-3 text-sm font-medium">#{{ order.order_number }}</td>
              <td class="px-4 py-3 text-sm">{{ order.user?.email }}</td>
              <td class="px-4 py-3 text-sm">{{ order.items?.[0]?.product?.name }}</td>
              <td class="px-4 py-3">
                <span 
                  :class="getStatusClass(order.status)"
                  class="px-2 py-1 text-xs rounded-full"
                >
                  {{ order.status }}
                </span>
              </td>
              <td class="px-4 py-3 text-sm font-medium">${{ order.total_amount.toFixed(2) }}</td>
              <td class="px-4 py-3 text-sm text-gray-600">{{ formatDate(order.created_at) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { DollarSign, Users, Package, ShoppingCart } from 'lucide-vue-next'
import { definePageMeta, useAsyncData, useSeoMeta } from '#imports'

definePageMeta({
  layout: 'admin',
  middleware: 'admin'
})

const { data: stats } = await useAsyncData('admin-stats', () => 
  $fetch('/api/admin/stats')
)

const { data: recentOrders } = await useAsyncData('recent-orders', () => 
  $fetch('/api/admin/orders?limit=10')
)

const getStatusClass = (status) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800'
    case 'pending': return 'bg-yellow-100 text-yellow-800'
    case 'declined': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

useSeoMeta({
  title: 'Admin Dashboard',
  description: 'Admin overview and statistics'
})
</script>
