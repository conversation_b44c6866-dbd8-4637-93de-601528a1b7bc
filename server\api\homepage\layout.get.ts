import { defineEvent<PERSON><PERSON><PERSON>, createError } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  const { data: userData } = await supabase.from("users").select("tenant_id").eq("id", user.id).single()

  const { data: layout } = await supabase
    .from("homepage_layout")
    .select("*")
    .eq("tenant_id", userData.tenant_id)
    .is("deleted_at", null)
    .order("sort_order")

  return layout || []
})
