<template>
  <div class="card p-4">
    <div class="aspect-square overflow-hidden rounded-lg">
      <img 
        :src="product.image_url || '/placeholder.svg?height=300&width=300'" 
        :alt="product.name"
        class="w-full h-full object-cover"
      />
    </div>
    
    <h3 class="font-semibold text-lg mb-2">{{ product.name }}</h3>
    <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ product.description }}</p>
    
    <div class="flex items-center justify-between">
      <div class="flex flex-col">
        <span v-if="pricing?.discount_price" class="text-lg font-bold text-primary">
          ${{ pricing.discount_price.toFixed(2) }}
        </span>
        <span 
          v-if="pricing?.user_price"
          :class="pricing?.discount_price ? 'text-sm text-gray-500 line-through' : 'text-lg font-bold text-primary'"
        >
          ${{ pricing.user_price.toFixed(2) }}
        </span>
        <span v-else class="text-lg font-bold text-gray-500">
          Price not set
        </span>
      </div>
      
      <button 
        @click="addToCart" 
        :disabled="!pricing?.user_price"
        class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {{ pricing?.user_price ? 'Add to Cart' : 'Unavailable' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { useCart } from '~/composables/useCart'

const cart = useCart()

const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  pricing: {
    type: Object,
    default: null
  }
})

const addToCart = () => {
  const price = props.pricing?.discount_price || props.pricing?.user_price || 0
  
  cart.addToCart({
    id: `${props.product.id}-${Date.now()}`,
    product_id: props.product.id,
    name: props.product.name,
    image_url: props.product.image_url,
    price: price
  })
}
</script>
