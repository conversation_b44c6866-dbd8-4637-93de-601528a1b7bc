<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Custom Fields</h3>
      <button @click="addField" class="btn-primary">
        <Plus class="w-4 h-4 mr-2" />
        Add Field
      </button>
    </div>
    
    <div v-if="localFields.length === 0" class="text-center py-8 text-gray-500">
      No custom fields added yet. Click "Add Field" to get started.
    </div>
    
    <div v-else class="space-y-4">
      <div 
        v-for="(field, index) in localFields" 
        :key="field.id || index"
        class="card p-4"
      >
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">Field {{ index + 1 }}</h4>
          <button @click="removeField(index)" class="text-red-600 hover:text-red-800">
            <Trash2 class="w-4 h-4" />
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium mb-2">Field Type *</label>
            <select v-model="field.field_type" class="input" required>
              <option value="">Select type</option>
              <option value="text">Text Input</option>
              <option value="dropdown">Dropdown</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Label *</label>
            <input 
              v-model="field.label"
              type="text" 
              class="input"
              placeholder="Enter field label"
              required
            />
          </div>
          
          <div class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">Hint Text</label>
            <input 
              v-model="field.hint"
              type="text" 
              class="input"
              placeholder="Optional hint for users"
            />
          </div>
          
          <div class="md:col-span-2">
            <label class="flex items-center">
              <input 
                v-model="field.is_required"
                type="checkbox" 
                class="mr-2"
              />
              Required field
            </label>
          </div>
          
          <div v-if="field.field_type === 'dropdown'" class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">Options</label>
            <div class="space-y-2">
              <div 
                v-for="(option, optionIndex) in field.options" 
                :key="optionIndex"
                class="flex items-center space-x-2"
              >
                <input 
                  v-model="option.option_value"
                  type="text" 
                  class="input flex-1"
                  placeholder="Option value"
                />
                <button 
                  @click="removeOption(index, optionIndex)" 
                  class="text-red-600 hover:text-red-800"
                >
                  <Trash2 class="w-4 h-4" />
                </button>
              </div>
              <button 
                @click="addOption(index)" 
                class="btn-outline w-full"
              >
                Add Option
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Plus, Trash2 } from 'lucide-vue-next'
import { ref, watch } from 'vue'

const props = defineProps({
  fields: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:fields'])

const localFields = ref([...props.fields])

const addField = () => {
  localFields.value.push({
    field_type: '',
    label: '',
    hint: '',
    is_required: false,
    sort_order: localFields.value.length,
    options: []
  })
}

const removeField = (index) => {
  localFields.value.splice(index, 1)
}

const addOption = (fieldIndex) => {
  if (!localFields.value[fieldIndex].options) {
    localFields.value[fieldIndex].options = []
  }
  localFields.value[fieldIndex].options.push({
    option_value: '',
    sort_order: localFields.value[fieldIndex].options.length
  })
}

const removeOption = (fieldIndex, optionIndex) => {
  localFields.value[fieldIndex].options.splice(optionIndex, 1)
}

watch(localFields, (newValue) => {
  emit('update:fields', newValue)
}, { deep: true })

watch(() => props.fields, (newValue) => {
  localFields.value = [...newValue]
}, { deep: true })
</script>
