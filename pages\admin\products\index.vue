<template>
  <div>
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold">Products</h1>
      <NuxtLink to="/admin/products/create" class="btn-primary">
        <Plus class="w-4 h-4 mr-2" />
        Add Product
      </NuxtLink>
    </div>

    <div class="card">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="product in products" :key="product.id">
              <td class="px-4 py-3">
                <div class="flex items-center space-x-3">
                  <img 
                    :src="product.image_url || '/placeholder.svg?height=40&width=40'" 
                    :alt="product.name"
                    class="w-10 h-10 object-cover rounded"
                  />
                  <div>
                    <div class="font-medium">{{ product.name }}</div>
                    <div class="text-sm text-gray-600">{{ product.description?.substring(0, 50) }}...</div>
                  </div>
                </div>
              </td>
              <td class="px-4 py-3 text-sm">{{ product.category?.name || 'No category' }}</td>
              <td class="px-4 py-3 text-sm font-medium">
                <span v-if="product.pricing?.user_price">
                  ${{ product.pricing.user_price.toFixed(2) }}
                </span>
                <span v-else-if="product.has_packages" class="text-gray-500">
                  Package pricing
                </span>
                <span v-else class="text-gray-500">No pricing</span>
              </td>
              <td class="px-4 py-3">
                <span 
                  :class="product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  class="px-2 py-1 text-xs rounded-full"
                >
                  {{ product.is_active ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="px-4 py-3">
                <div class="flex items-center space-x-2">
                  <NuxtLink 
                    :to="`/admin/products/${product.id}/edit`"
                    class="text-indigo-600 hover:text-indigo-800"
                  >
                    <Edit class="w-4 h-4" />
                  </NuxtLink>
                  <button 
                    @click="deleteProduct(product.id)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Plus, Edit, Trash2 } from 'lucide-vue-next'
import { definePageMeta, useAsyncData, useSeoMeta } from '#imports'
import { $fetch } from 'ofetch'

const { data: products, refresh } = await useAsyncData('admin-products', () => 
  $fetch('/api/admin/products')
)

const deleteProduct = async (productId) => {
  if (!confirm('Are you sure you want to delete this product?')) return

  try {
    await $fetch(`/api/admin/products/${productId}`, {
      method: 'DELETE'
    })
    await refresh()
  } catch (error) {
    console.error('Failed to delete product:', error)
  }
}

definePageMeta({
  layout: 'admin',
  middleware: 'admin'
})

useSeoMeta({
  title: 'Products - Admin',
  description: 'Manage your products'
})
</script>
