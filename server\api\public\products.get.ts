import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from "h3"
import { serverSupabaseClient } from "#supabase/server"
import { getQuery } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const query = getQuery(event)

  // For demo purposes, let's use the first tenant
  const { data: tenant } = await supabase.from("tenants").select("id").limit(1).single()

  if (!tenant) {
    return []
  }

  let productsQuery = supabase
    .from("products")
    .select(`
      *,
      category:categories(*),
      pricing:product_pricing(*)
    `)
    .eq("tenant_id", tenant.id)
    .eq("is_active", true)
    .is("deleted_at", null)

  if (query.category) {
    productsQuery = productsQuery.eq("category_id", query.category)
  }

  if (query.featured) {
    productsQuery = productsQuery.limit(Number.parseInt(query.limit as string) || 6)
  }

  const { data: products } = await productsQuery.order("created_at", { ascending: false })

  return products || []
})
