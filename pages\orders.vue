<template>
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-2xl font-bold mb-8">My Orders</h1>
    
    <div v-if="pending" class="space-y-4">
      <div v-for="i in 3" :key="i" class="card p-6 animate-pulse">
        <div class="flex items-center justify-between mb-4">
          <div class="h-4 bg-gray-200 rounded w-32"></div>
          <div class="h-6 bg-gray-200 rounded w-20"></div>
        </div>
        <div class="h-3 bg-gray-200 rounded w-48 mb-2"></div>
        <div class="h-3 bg-gray-200 rounded w-24"></div>
      </div>
    </div>
    
    <div v-else-if="orders.length === 0" class="text-center py-12">
      <div class="text-gray-500 mb-4">No orders found</div>
      <NuxtLink to="/shop" class="btn-primary">Start Shopping</NuxtLink>
    </div>
    
    <div v-else class="space-y-6">
      <div 
        v-for="order in orders" 
        :key="order.id"
        class="card p-6"
      >
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="font-semibold">Order #{{ order.order_number }}</h3>
            <p class="text-sm text-gray-600">{{ formatDate(order.created_at) }}</p>
          </div>
          <div class="flex items-center space-x-3">
            <span 
              :class="getStatusClass(order.status)"
              class="px-3 py-1 text-sm rounded-full"
            >
              {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
            </span>
            <span class="font-semibold">${{ order.total_amount.toFixed(2) }}</span>
          </div>
        </div>
        
        <div v-if="order.decline_reason" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-sm text-red-800">
            <strong>Decline Reason:</strong> {{ order.decline_reason }}
          </p>
        </div>
        
        <div class="space-y-3">
          <div 
            v-for="item in order.items" 
            :key="item.id"
            class="flex items-center justify-between py-3 border-b last:border-b-0"
          >
            <div class="flex items-center space-x-4">
              <img 
                :src="item.product?.image_url || '/placeholder.svg?height=60&width=60'" 
                :alt="item.product?.name"
                class="w-12 h-12 object-cover rounded"
              />
              <div>
                <h4 class="font-medium">{{ item.product?.name }}</h4>
                <p v-if="item.package" class="text-sm text-gray-600">
                  Package: {{ item.package.name }}
                </p>
                <p class="text-sm text-gray-600">Qty: {{ item.quantity }}</p>
              </div>
            </div>
            
            <div class="text-right">
              <div class="font-semibold">${{ item.total_price.toFixed(2) }}</div>
              <div v-if="item.digital_code && order.status === 'completed'" class="mt-2">
                <button 
                  @click="copyCode(item.digital_code.code)"
                  class="btn-outline text-xs"
                >
                  <Copy class="w-3 h-3 mr-1" />
                  Copy Code
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Copy } from 'lucide-vue-next'
import { definePageMeta, useAsyncData, useSeoMeta } from '#imports'
import { $fetch } from 'ofetch'

definePageMeta({
  middleware: 'auth'
})

const { data: orders, pending } = await useAsyncData('orders', () => 
  $fetch('/api/user/orders')
)

const getStatusClass = (status) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800'
    case 'pending': return 'bg-yellow-100 text-yellow-800'
    case 'declined': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    // You could add a toast notification here
  } catch (err) {
    console.error('Failed to copy code:', err)
  }
}

useSeoMeta({
  title: 'Orders - Store',
  description: 'View your order history'
})
</script>
