import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"
import { createError } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  // Verify admin role
  const { data: userData } = await supabase.from("users").select("tenant_id, role").eq("id", user.id).single()

  if (userData?.role !== "admin") {
    throw createError({
      statusCode: 403,
      statusMessage: "Access denied",
    })
  }

  const { data: products } = await supabase
    .from("products")
    .select(`
      *,
      category:categories(id, name),
      pricing:product_pricing(*),
      packages:product_packages(*)
    `)
    .eq("tenant_id", userData.tenant_id)
    .is("deleted_at", null)
    .order("created_at", { ascending: false })

  return products || []
})
