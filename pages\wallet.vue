<template>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-2xl font-bold mb-8">My Wallet</h1>
    
    <!-- Wallet Balances -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div 
        v-for="wallet in wallets" 
        :key="wallet.id"
        class="card p-6 text-center"
      >
        <div class="text-3xl font-bold text-primary mb-2">
          ${{ wallet.balance.toFixed(2) }}
        </div>
        <div class="text-sm text-gray-600">{{ wallet.currency }}</div>
      </div>
    </div>
    
    <!-- Deposit Button -->
    <div class="mb-8">
      <button @click="showDepositModal = true" class="btn-primary">
        <Plus class="w-4 h-4 mr-2" />
        Add Funds
      </button>
    </div>
    
    <!-- Transaction History -->
    <div class="card">
      <div class="p-4 border-b">
        <h2 class="text-lg font-semibold">Transaction History</h2>
      </div>
      
      <div v-if="pending" class="p-4">
        <div v-for="i in 5" :key="i" class="flex items-center justify-between py-3 animate-pulse">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
            <div>
              <div class="h-4 bg-gray-200 rounded w-24 mb-1"></div>
              <div class="h-3 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
          <div class="h-4 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
      
      <div v-else-if="transactions.length === 0" class="p-8 text-center text-gray-500">
        No transactions yet
      </div>
      
      <div v-else class="divide-y">
        <div 
          v-for="transaction in transactions" 
          :key="transaction.id"
          class="p-4 flex items-center justify-between"
        >
          <div class="flex items-center space-x-3">
            <div 
              :class="getTransactionIconClass(transaction.type)"
              class="w-8 h-8 rounded-full flex items-center justify-center"
            >
              <component :is="getTransactionIcon(transaction.type)" class="w-4 h-4" />
            </div>
            <div>
              <div class="font-medium">{{ transaction.description }}</div>
              <div class="text-sm text-gray-600">
                {{ formatDate(transaction.created_at) }}
              </div>
            </div>
          </div>
          <div 
            :class="transaction.type === 'deposit' || transaction.type === 'refund' ? 'text-green-600' : 'text-red-600'"
            class="font-semibold"
          >
            {{ transaction.type === 'deposit' || transaction.type === 'refund' ? '+' : '-' }}${{ transaction.amount.toFixed(2) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Deposit Modal -->
    <div v-if="showDepositModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">Add Funds</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">Amount</label>
            <input 
              v-model.number="depositAmount"
              type="number" 
              step="0.01"
              min="1"
              class="input"
              placeholder="0.00"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Payment Method</label>
            <select v-model="paymentMethod" class="input">
              <option value="">Select payment method</option>
              <option value="credit_card">Credit Card</option>
              <option value="paypal">PayPal</option>
              <option value="bank_transfer">Bank Transfer</option>
            </select>
          </div>
        </div>
        
        <div class="flex justify-end space-x-2 mt-6">
          <button @click="showDepositModal = false" class="btn-outline">Cancel</button>
          <button @click="processDeposit" class="btn-primary" :disabled="!depositAmount || !paymentMethod">
            Add Funds
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus, ArrowUpCircle, ArrowDownCircle, ShoppingCart, RotateCcw } from 'lucide-vue-next'
import { definePageMeta, useAsyncData, useSeoMeta } from '#imports'
import { $fetch } from 'ofetch'
import { refreshCookie } from '~/composables/useRefreshCookie'

definePageMeta({
  middleware: 'auth'
})

const showDepositModal = ref(false)
const depositAmount = ref(0)
const paymentMethod = ref('')

const { data: wallets } = await useAsyncData('wallets', () => 
  $fetch('/api/user/wallets')
)

const { data: transactions, pending } = await useAsyncData('transactions', () => 
  $fetch('/api/user/transactions')
)

const getTransactionIcon = (type) => {
  switch (type) {
    case 'deposit': return ArrowUpCircle
    case 'withdrawal': return ArrowDownCircle
    case 'purchase': return ShoppingCart
    case 'refund': return RotateCcw
    default: return ArrowUpCircle
  }
}

const getTransactionIconClass = (type) => {
  switch (type) {
    case 'deposit': return 'bg-green-100 text-green-600'
    case 'withdrawal': return 'bg-red-100 text-red-600'
    case 'purchase': return 'bg-blue-100 text-blue-600'
    case 'refund': return 'bg-yellow-100 text-yellow-600'
    default: return 'bg-gray-100 text-gray-600'
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const processDeposit = async () => {
  try {
    await $fetch('/api/user/deposit', {
      method: 'POST',
      body: {
        amount: depositAmount.value,
        payment_method: paymentMethod.value
      }
    })
    
    // Refresh data
    await refreshCookie('wallets')
    await refreshCookie('transactions')
    
    // Reset form
    depositAmount.value = 0
    paymentMethod.value = ''
    showDepositModal.value = false
  } catch (error) {
    console.error('Deposit failed:', error)
  }
}

useSeoMeta({
  title: 'Wallet - Store',
  description: 'Manage your wallet and transactions'
})
</script>
