import { defineEvent<PERSON><PERSON><PERSON>, createError } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"
import { getQuery } from "#app/utils"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)
  const query = getQuery(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  const { data: userData } = await supabase.from("users").select("tenant_id").eq("id", user.id).single()

  let productsQuery = supabase
    .from("products")
    .select(`
      *,
      category:categories(*),
      pricing:product_pricing(*),
      packages:product_packages(*)
    `)
    .eq("tenant_id", userData.tenant_id)
    .eq("is_active", true)
    .is("deleted_at", null)

  if (query.category) {
    productsQuery = productsQuery.eq("category_id", query.category)
  }

  if (query.featured) {
    productsQuery = productsQuery.limit(Number.parseInt(query.limit as string) || 6)
  }

  const { data: products } = await productsQuery.order("created_at", { ascending: false })

  return products || []
})
