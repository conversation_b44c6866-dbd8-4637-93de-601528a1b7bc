<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">Digital Codes</h3>
      <button @click="showAddModal = true" class="btn-primary">
        <Plus class="w-4 h-4 mr-2" />
        Add Codes
      </button>
    </div>
    
    <div class="card p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-green-600">{{ availableCodes }}</div>
          <div class="text-sm text-gray-600">Available</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-red-600">{{ usedCodes }}</div>
          <div class="text-sm text-gray-600">Used</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-blue-600">{{ totalCodes }}</div>
          <div class="text-sm text-gray-600">Total</div>
        </div>
      </div>
    </div>
    
    <div v-if="codes.length > 0" class="card">
      <div class="p-4 border-b">
        <h4 class="font-medium">Code List</h4>
      </div>
      <div class="max-h-64 overflow-y-auto">
        <div 
          v-for="code in codes" 
          :key="code.id"
          class="flex items-center justify-between p-4 border-b last:border-b-0"
        >
          <div class="flex items-center space-x-3">
            <span class="font-mono text-sm">{{ maskCode(code.code) }}</span>
            <span 
              :class="code.is_used ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'"
              class="px-2 py-1 text-xs rounded-full"
            >
              {{ code.is_used ? 'Used' : 'Available' }}
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <button 
              @click="copyCode(code.code)" 
              class="text-blue-600 hover:text-blue-800"
              title="Copy code"
            >
              <Copy class="w-4 h-4" />
            </button>
            <button 
              @click="deleteCode(code.id)" 
              class="text-red-600 hover:text-red-800"
              title="Delete code"
            >
              <Trash2 class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Add Codes Modal -->
    <div v-if="showAddModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">Add Digital Codes</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">Codes (one per line)</label>
            <textarea 
              v-model="newCodes"
              class="input min-h-32"
              placeholder="Enter codes, one per line"
            ></textarea>
          </div>
        </div>
        
        <div class="flex justify-end space-x-2 mt-6">
          <button @click="showAddModal = false" class="btn-outline">Cancel</button>
          <button @click="addCodes" class="btn-primary">Add Codes</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Plus, Copy, Trash2 } from 'lucide-vue-next'
import { $fetch } from 'ohmyfetch'

const props = defineProps({
  productId: {
    type: String,
    required: true
  },
  packageId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['codes-updated'])

const showAddModal = ref(false)
const newCodes = ref('')
const codes = ref([])

const availableCodes = computed(() => codes.value.filter(c => !c.is_used).length)
const usedCodes = computed(() => codes.value.filter(c => c.is_used).length)
const totalCodes = computed(() => codes.value.length)

const maskCode = (code) => {
  if (code.length <= 4) return code
  return code.substring(0, 2) + '*'.repeat(code.length - 4) + code.substring(code.length - 2)
}

const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    // You could add a toast notification here
  } catch (err) {
    console.error('Failed to copy code:', err)
  }
}

const addCodes = async () => {
  const codeList = newCodes.value.split('\n').filter(code => code.trim())
  
  if (codeList.length === 0) return
  
  try {
    const { data } = await $fetch('/api/admin/digital-codes', {
      method: 'POST',
      body: {
        product_id: props.productId,
        package_id: props.packageId,
        codes: codeList
      }
    })
    
    codes.value.push(...data)
    newCodes.value = ''
    showAddModal.value = false
    emit('codes-updated')
  } catch (error) {
    console.error('Failed to add codes:', error)
  }
}

const deleteCode = async (codeId) => {
  if (!confirm('Are you sure you want to delete this code?')) return
  
  try {
    await $fetch(`/api/admin/digital-codes/${codeId}`, {
      method: 'DELETE'
    })
    
    codes.value = codes.value.filter(c => c.id !== codeId)
    emit('codes-updated')
  } catch (error) {
    console.error('Failed to delete code:', error)
  }
}

const loadCodes = async () => {
  try {
    const { data } = await $fetch('/api/admin/digital-codes', {
      query: {
        product_id: props.productId,
        package_id: props.packageId
      }
    })
    codes.value = data
  } catch (error) {
    console.error('Failed to load codes:', error)
  }
}

onMounted(() => {
  loadCodes()
})
</script>
