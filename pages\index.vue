<template>
  <div>
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Welcome to {{ storeName }}
        </h1>
        <p class="text-xl mb-8 max-w-2xl mx-auto">
          Discover amazing products and start shopping today
        </p>
        <div class="space-x-4">
          <NuxtLink to="/shop" class="btn-primary bg-white text-blue-600 hover:bg-gray-100">
            Shop Now
          </NuxtLink>
          <NuxtLink v-if="!user" to="/auth/register" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
            Sign Up
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section v-if="categories && categories.length > 0" class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">Shop by Category</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <NuxtLink 
            v-for="category in categories" 
            :key="category.id"
            :to="`/shop?category=${category.id}`"
            class="group"
          >
            <div class="card p-6 text-center hover:shadow-lg transition-shadow">
              <div class="aspect-square mb-4 overflow-hidden rounded-lg bg-gray-100">
                <img 
                  :src="category.image_url || '/placeholder.svg?height=200&width=200&text=' + encodeURIComponent(category.name)" 
                  :alt="category.name"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform"
                />
              </div>
              <h3 class="font-medium">{{ category.name }}</h3>
            </div>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Featured Products Section -->
    <section v-if="products && products.length > 0" class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">Featured Products</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProductCard 
            v-for="product in products" 
            :key="product.id"
            :product="product"
            :pricing="product.pricing?.[0]"
          />
        </div>
      </div>
    </section>

    <!-- Fallback content if no data -->
    <section v-if="(!categories || categories.length === 0) && (!products || products.length === 0)" class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-8">
          <h2 class="text-2xl font-bold text-yellow-800 mb-4">Store Setup Required</h2>
          <p class="text-yellow-700 mb-6">
            It looks like your store hasn't been set up yet. You need to add some categories and products to get started.
          </p>
          <div class="space-x-4">
            <NuxtLink v-if="user" to="/admin" class="btn-primary">
              Go to Admin Panel
            </NuxtLink>
            <NuxtLink v-else to="/auth/login" class="btn-primary">
              Login to Admin
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useSupabaseUser } from '#imports'
import { useAsyncData } from '#app'
import { useSeoMeta } from '#head'
import ProductCard from '~/components/ProductCard.vue'

const user = useSupabaseUser()
const storeName = 'Demo Store'

// Fetch data without authentication requirement for now
const { data: categories } = await useAsyncData('categories', async () => {
  try {
    return await $fetch('/api/public/categories')
  } catch (error) {
    console.log('Categories not available:', error)
    return []
  }
})

const { data: products } = await useAsyncData('featured-products', async () => {
  try {
    return await $fetch('/api/public/products?featured=true&limit=6')
  } catch (error) {
    console.log('Products not available:', error)
    return []
  }
})

useSeoMeta({
  title: `${storeName} - Home`,
  description: 'Welcome to our online store'
})
</script>
