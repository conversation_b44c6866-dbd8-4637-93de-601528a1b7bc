import { defineNuxtRouteMiddleware, navigateTo } from "#app"
import { useSupabaseUser } from "#supabase"

export default defineNuxtRouteMiddleware(async (to, from) => {
  const user = useSupabaseUser()

  if (!user.value) {
    return navigateTo("/auth/login")
  }

  // For now, let's skip the admin role check to test the interface
  // You can enable this later when you have proper user roles set up
  /*
  try {
    const { data } = await $fetch('/api/user/profile')
    if (data?.role !== 'admin') {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      })
    }
  } catch (error) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied'
    })
  }
  */
})
