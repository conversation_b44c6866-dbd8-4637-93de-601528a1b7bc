<template>
  <header class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <NuxtLink to="/" class="text-xl font-bold text-primary">
            {{ tenant?.name || 'Store' }}
          </NuxtLink>
        </div>
        
        <nav class="hidden md:flex space-x-8">
          <NuxtLink to="/" class="text-gray-700 hover:text-primary">Home</NuxtLink>
          <NuxtLink to="/shop" class="text-gray-700 hover:text-primary">Shop</NuxtLink>
          <NuxtLink to="/wallet" class="text-gray-700 hover:text-primary" v-if="user">Wallet</NuxtLink>
          <NuxtLink to="/orders" class="text-gray-700 hover:text-primary" v-if="user">Orders</NuxtLink>
        </nav>
        
        <div class="flex items-center space-x-4">
          <div v-if="user" class="flex items-center space-x-2">
            <span class="text-sm text-gray-700">{{ user.email }}</span>
            <button @click="signOut" class="btn-outline">Sign Out</button>
          </div>
          <div v-else class="space-x-2">
            <NuxtLink to="/auth/login" class="btn-outline">Login</NuxtLink>
            <NuxtLink to="/auth/register" class="btn-primary">Register</NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { useSupabaseClient, useSupabaseUser } from '#imports'
import { useTenant } from '~/composables/useTenant'
import { navigateTo } from '#app'

const supabase = useSupabaseClient()
const user = useSupabaseUser()
const { data: tenant } = useTenant()

const signOut = async () => {
  await supabase.auth.signOut()
  await navigateTo('/auth/login')
}
</script>
