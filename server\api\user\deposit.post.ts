import { defineEvent<PERSON><PERSON><PERSON>, readBody } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"
import { createError } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)
  const body = await readBody(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  const { data: userData } = await supabase.from("users").select("tenant_id").eq("id", user.id).single()

  // Get or create wallet
  let { data: wallet } = await supabase
    .from("wallets")
    .select("*")
    .eq("tenant_id", userData.tenant_id)
    .eq("user_id", user.id)
    .eq("currency", "USD")
    .single()

  if (!wallet) {
    const { data: newWallet } = await supabase
      .from("wallets")
      .insert({
        tenant_id: userData.tenant_id,
        user_id: user.id,
        currency: "USD",
        balance: 0,
      })
      .select()
      .single()
    wallet = newWallet
  }

  // Update wallet balance
  const newBalance = Number.parseFloat(wallet.balance) + Number.parseFloat(body.amount)
  await supabase.from("wallets").update({ balance: newBalance }).eq("id", wallet.id)

  // Create transaction record
  await supabase.from("wallet_transactions").insert({
    tenant_id: userData.tenant_id,
    wallet_id: wallet.id,
    user_id: user.id,
    type: "deposit",
    amount: body.amount,
    description: `Deposit via ${body.payment_method}`,
    status: "completed",
  })

  return { success: true }
})
