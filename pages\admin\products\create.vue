<template>
  <div>
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold">Create Product</h1>
      <NuxtLink to="/admin/products" class="btn-outline">
        <ArrowLeft class="w-4 h-4 mr-2" />
        Back to Products
      </NuxtLink>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-8">
      <!-- Tabs -->
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            type="button"
            @click="activeTab = tab.id"
            :class="activeTab === tab.id 
              ? 'border-indigo-500 text-indigo-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          >
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Basic Info Tab -->
      <div v-show="activeTab === 'basic'" class="card p-6">
        <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">Product Name *</label>
            <input 
              v-model="form.name"
              type="text" 
              class="input"
              placeholder="Enter product name"
              required
            />
          </div>
          
          <div class="md:col-span-2">
            <label class="block text-sm font-medium mb-2">Description</label>
            <textarea 
              v-model="form.description"
              class="input min-h-24"
              placeholder="Enter product description"
            ></textarea>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Category</label>
            <select v-model="form.category_id" class="input">
              <option value="">Select category</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">Image URL</label>
            <input 
              v-model="form.image_url"
              type="url" 
              class="input"
              placeholder="https://example.com/image.jpg"
            />
          </div>
          
          <div class="md:col-span-2">
            <div class="space-y-3">
              <label class="flex items-center">
                <input 
                  v-model="form.has_packages"
                  type="checkbox" 
                  class="mr-2"
                />
                This product has packages
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="form.has_codes"
                  type="checkbox" 
                  class="mr-2"
                />
                This product has digital codes
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="form.has_custom_fields"
                  type="checkbox" 
                  class="mr-2"
                />
                This product has custom fields
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="form.is_active"
                  type="checkbox" 
                  class="mr-2"
                />
                Product is active
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Tab -->
      <div v-show="activeTab === 'pricing' && !form.has_packages" class="card p-6">
        <PricingBlock 
          :pricing="form.pricing"
          @update:pricing="form.pricing = $event"
        />
      </div>

      <!-- Packages Tab -->
      <div v-show="activeTab === 'packages' && form.has_packages" class="card p-6">
        <PackageEditor 
          :packages="form.packages"
          @update:packages="form.packages = $event"
        />
      </div>

      <!-- Custom Fields Tab -->
      <div v-show="activeTab === 'fields' && form.has_custom_fields" class="card p-6">
        <CustomFieldBuilder 
          :fields="form.custom_fields"
          @update:fields="form.custom_fields = $event"
        />
      </div>

      <!-- Digital Codes Tab -->
      <div v-show="activeTab === 'codes' && form.has_codes" class="card p-6">
        <div class="text-center py-8 text-gray-500">
          Digital codes can be managed after the product is created.
        </div>
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end space-x-4">
        <NuxtLink to="/admin/products" class="btn-outline">Cancel</NuxtLink>
        <button type="submit" :disabled="loading" class="btn-primary">
          {{ loading ? 'Creating...' : 'Create Product' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ArrowLeft } from 'lucide-vue-next'
import { definePageMeta, useAsyncData, navigateTo } from '#imports'
import { $fetch } from 'ofetch'
import PricingBlock from '~/components/PricingBlock.vue'
import PackageEditor from '~/components/PackageEditor.vue'
import CustomFieldBuilder from '~/components/CustomFieldBuilder.vue'

const activeTab = ref('basic')
const loading = ref(false)

const { data: categories } = useAsyncData('categories', () => 
  $fetch('/api/admin/categories')
)

const form = ref({
  name: '',
  description: '',
  category_id: '',
  image_url: '',
  has_packages: false,
  has_codes: false,
  has_custom_fields: false,
  is_active: true,
  pricing: {
    original_price: 0,
    user_price: 0,
    distributor_price: null,
    discount_price: null
  },
  packages: [],
  custom_fields: []
})

const tabs = computed(() => [
  { id: 'basic', name: 'Basic Info' },
  { id: 'pricing', name: 'Pricing' },
  { id: 'packages', name: 'Packages' },
  { id: 'fields', name: 'Custom Fields' },
  { id: 'codes', name: 'Digital Codes' }
].filter(tab => {
  if (tab.id === 'pricing') {
    return !form.value.has_packages
  }
  if (tab.id === 'packages') {
    return form.value.has_packages
  }
  if (tab.id === 'fields') {
    return form.value.has_custom_fields
  }
  if (tab.id === 'codes') {
    return form.value.has_codes
  }
  return true
}))

const handleSubmit = async () => {
  loading.value = true

  try {
    await $fetch('/api/admin/products', {
      method: 'POST',
      body: form.value
    })

    await navigateTo('/admin/products')
  } catch (error) {
    console.error('Failed to create product:', error)
  } finally {
    loading.value = false
  }
}

definePageMeta({
  layout: 'admin',
  middleware: 'admin'
})
</script>
