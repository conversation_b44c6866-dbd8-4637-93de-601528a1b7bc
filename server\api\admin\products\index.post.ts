import { defineEvent<PERSON><PERSON><PERSON>, readBody } from "h3"
import { serverSupabaseClient, serverSupabaseUser } from "#supabase/server"
import { createError } from "h3"

export default defineEventHandler(async (event) => {
  const supabase = serverSupabaseClient(event)
  const user = await serverSupabaseUser(event)
  const body = await readBody(event)

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Unauthorized",
    })
  }

  // Verify admin role
  const { data: userData } = await supabase.from("users").select("tenant_id, role").eq("id", user.id).single()

  if (userData?.role !== "admin") {
    throw createError({
      statusCode: 403,
      statusMessage: "Access denied",
    })
  }

  // Create product
  const { data: product, error: productError } = await supabase
    .from("products")
    .insert({
      tenant_id: userData.tenant_id,
      name: body.name,
      description: body.description,
      category_id: body.category_id || null,
      image_url: body.image_url,
      has_packages: body.has_packages,
      has_codes: body.has_codes,
      has_custom_fields: body.has_custom_fields,
      is_active: body.is_active,
    })
    .select()
    .single()

  if (productError) {
    throw createError({
      statusCode: 400,
      statusMessage: productError.message,
    })
  }

  // Create pricing if not using packages
  if (!body.has_packages && body.pricing) {
    await supabase.from("product_pricing").insert({
      tenant_id: userData.tenant_id,
      product_id: product.id,
      original_price: body.pricing.original_price,
      user_price: body.pricing.user_price,
      distributor_price: body.pricing.distributor_price,
      discount_price: body.pricing.discount_price,
    })
  }

  // Create packages if enabled
  if (body.has_packages && body.packages?.length > 0) {
    const packages = body.packages.map((pkg: any) => ({
      tenant_id: userData.tenant_id,
      product_id: product.id,
      name: pkg.name,
      image_url: pkg.image_url,
      original_price: pkg.original_price,
      user_price: pkg.user_price,
      distributor_price: pkg.distributor_price,
      discount_price: pkg.discount_price,
      sort_order: pkg.sort_order,
    }))

    await supabase.from("product_packages").insert(packages)
  }

  // Create custom fields if enabled
  if (body.has_custom_fields && body.custom_fields?.length > 0) {
    for (const field of body.custom_fields) {
      const { data: customField } = await supabase
        .from("product_custom_fields")
        .insert({
          tenant_id: userData.tenant_id,
          product_id: product.id,
          field_type: field.field_type,
          label: field.label,
          hint: field.hint,
          is_required: field.is_required,
          sort_order: field.sort_order,
        })
        .select()
        .single()

      // Create field options for dropdown fields
      if (field.field_type === "dropdown" && field.options?.length > 0) {
        const options = field.options.map((option: any) => ({
          tenant_id: userData.tenant_id,
          field_id: customField.id,
          option_value: option.option_value,
          sort_order: option.sort_order,
        }))

        await supabase.from("custom_field_options").insert(options)
      }
    }
  }

  return { data: product }
})
