<template>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-2xl font-bold mb-8">Shopping Cart</h1>
    
    <div v-if="cart.items.value.length === 0" class="text-center py-12">
      <ShoppingCart class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <p class="text-gray-500 mb-4">Your cart is empty</p>
      <NuxtLink to="/shop" class="btn-primary">Continue Shopping</NuxtLink>
    </div>
    
    <div v-else class="space-y-6">
      <!-- Cart Items -->
      <div class="card">
        <div class="p-4 border-b">
          <h2 class="text-lg font-semibold">Cart Items</h2>
        </div>
        
        <div class="divide-y">
          <div 
            v-for="item in cart.items.value" 
            :key="`${item.product_id}-${item.package_id || 'no-package'}`"
            class="p-4 flex items-center justify-between"
          >
            <div class="flex items-center space-x-4">
              <img 
                :src="item.image_url || '/placeholder.svg?height=60&width=60'" 
                :alt="item.name"
                class="w-12 h-12 object-cover rounded"
              />
              <div>
                <h3 class="font-medium">{{ item.name }}</h3>
                <p class="text-sm text-gray-600">${{ item.price.toFixed(2) }} each</p>
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <button 
                  @click="cart.updateQuantity(item.product_id, item.quantity - 1, item.package_id)"
                  class="w-8 h-8 rounded-full border flex items-center justify-center hover:bg-gray-50"
                >
                  <Minus class="w-4 h-4" />
                </button>
                <span class="w-8 text-center">{{ item.quantity }}</span>
                <button 
                  @click="cart.updateQuantity(item.product_id, item.quantity + 1, item.package_id)"
                  class="w-8 h-8 rounded-full border flex items-center justify-center hover:bg-gray-50"
                >
                  <Plus class="w-4 h-4" />
                </button>
              </div>
              
              <div class="text-right">
                <div class="font-semibold">${{ (item.price * item.quantity).toFixed(2) }}</div>
                <button 
                  @click="cart.removeFromCart(item.product_id, item.package_id)"
                  class="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Order Summary -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold mb-4">Order Summary</h2>
        
        <div class="space-y-2 mb-4">
          <div class="flex justify-between">
            <span>Subtotal ({{ cart.totalItems.value }} items)</span>
            <span>${{ cart.totalPrice.value.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between font-semibold text-lg border-t pt-2">
            <span>Total</span>
            <span>${{ cart.totalPrice.value.toFixed(2) }}</span>
          </div>
        </div>
        
        <button @click="proceedToCheckout" class="btn-primary w-full">
          Proceed to Checkout
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ShoppingCart, Plus, Minus } from 'lucide-vue-next'
import { useCart } from '~/composables/useCart'
import { navigateTo } from '#app'
import { useSeoMeta } from '#app'

const cart = useCart()

const proceedToCheckout = () => {
  navigateTo('/checkout')
}

useSeoMeta({
  title: 'Shopping Cart - Store',
  description: 'Review your cart items'
})
</script>
